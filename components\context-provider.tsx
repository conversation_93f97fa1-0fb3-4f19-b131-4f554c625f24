"use client";

import React from "react";
import { AuthUIProvider } from "@daveyplate/better-auth-ui"
import NextLink from "next/link"
import { useRouter } from "next/navigation"
import type { ReactNode } from "react"

const Link = ({ href, className, children }: { href: string; className?: string; children: ReactNode }) => (
  <NextLink href={href} className={className}>{children}</NextLink>
);

import { authClient } from "@/lib/auth-client"

export const ThemeContext = React.createContext({
  ip: { ip: "test" },
  updateIp: (ip: { ip: string }) => {},
  chatOpen: false,
  toggleChatOpen: () => {},
});

export const useThemeContext = () => {
  const context = React.useContext(ThemeContext);
  if (context === undefined) {
    throw new Error("useThemeContext must be used within a ThemeProvider");
  }
  return context;
};

export const ContextProvider = ({ children }: { children: any }) => {
  const [ip, setIp] = React.useState({ ip: "test" });
  const [chatOpen, setChatOpen] = React.useState(false);
  const router = useRouter()


  function updateIp(ip: { ip: string }) {
    setIp(ip);
  }
  function toggleChatOpen() {
    setChatOpen((prev) => !prev);
  }
  return (
    <ThemeContext.Provider value={{ ip, updateIp, chatOpen, toggleChatOpen }}>
      <AuthUIProvider
            authClient={authClient}
            navigate={router.push}
            replace={router.replace}
            onSessionChange={() => {
                // Clear router cache (protected routes)
                router.refresh()
            }}
            Link={Link}
        >
      {children}
      </AuthUIProvider>
    </ThemeContext.Provider>
  );
};
