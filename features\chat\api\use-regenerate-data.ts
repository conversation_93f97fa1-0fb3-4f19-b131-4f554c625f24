import { useMutation, useQueryClient } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { toast } from "sonner";

interface RegenerateDataParams {
  personaId: string;
  fullDesc: string;
  personaInfo: any;
}

export const useRegenerateData = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ personaId, fullDesc, personaInfo }: RegenerateDataParams) => {
      const response = await client.api.conversation.regenerateData.$post(
        { 
          json: { 
            fullDesc,
            personaInfo 
          } 
        },
        {
          headers: {
            "X-Persona-ID": personaId,
          },
        }
      );
      
      if (!response.ok) {
        throw new Error("Failed to regenerate data");
      }
      
      return response.json();
    },
    onSuccess: () => {
      toast.success("Data regenerated successfully");
      queryClient.invalidateQueries({ queryKey: ["transactions"] });
    },
    onError: () => {
      toast.error("Failed to regenerate data");
    }
  });
};
